# VSCode 智能建筑能效管理系统开发指南

## 1. 环境准备
### 1.1 安装依赖
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装核心依赖
pip install requests python-dotenv jupyter
pip install --upgrade openai  # 兼容OpenRouter接口
```

### 1.2 VSCode 扩展推荐
| 扩展 | 用途 |
|------|------|
| Python | 微软官方Python支持 |
| Jupyter | Notebook交互开发 |
| REST Client | API调试测试 |
| GitLens | 版本控制增强 |

## 2. 项目结构配置
```bash
# 创建标准项目结构
mkdir -p src/{config,utils,models}
touch src/{main.py,config.py,utils.py}
touch .env .gitignore requirements.txt
```

### 2.1 核心文件配置
```python
# config.py
OPENROUTER_API_KEY = "your_api_key_here"
API_URL = "https://openrouter.ai/api/v1/chat/completions"
DEFAULT_MODEL = "openai/gpt-4o"
```

```python
# main.py
import os
import json
import requests
from config import OPENROUTER_API_KEY, API_URL

def stream_chat_completion(question):
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "openai/gpt-4o",
        "messages": [{"role": "user", "content": question}],
        "stream": True
    }
    
    buffer = ""
    with requests.post(API_URL, headers=headers, json=payload, stream=True) as r:
        for chunk in r.iter_content(chunk_size=1024, decode_unicode=True):
            if chunk:
                buffer += chunk
                while True:
                    try:
                        line_end = buffer.find('\n')
                        if line_end == -1:
                            break
                        line = buffer[:line_end].strip()
                        buffer = buffer[line_end+1:]
                        
                        if line.startswith('data: '):
                            data = line[6:]
                            if data == '[DONE]':
                                return
                            try:
                                content = json.loads(data)["choices"][0]["delta"].get("content")
                                if content:
                                    print(content, end="", flush=True)
                            except Exception as e:
                                print(f"\n[ERROR] 解析响应失败: {e}")
                                continue
                    except Exception as e:
                        print(f"\n[CRITICAL] 流处理异常: {e}")
                        return
```

## 3. 开发工作流
### 3.1 调试配置 (launch.json)
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: 当前文件",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "subProcess": true,
      "justMyCode": true
    }
  ]
}
```

### 3.2 实时测试 (REST Client)
```http
POST https://openrouter.ai/api/v1/chat/completions
Content-Type: application/json
Authorization: Bearer {{OPENROUTER_API_KEY}}

{
  "model": "openai/gpt-4o",
  "messages": [{"role": "user", "content": "How would you build the tallest building ever?"}],
  "stream": true
}
```

## 4. 运行指南
### 4.1 基础执行
```bash
# 单次执行
python src/main.py

# 监听文件变更自动重启
pip install watchdog
python -m watchgod src/main.py
```

### 4.2 交互式开发
1. 在VSCode中打开Jupyter Notebook
2. 创建新单元格输入：
```python
from main import stream_chat_completion
stream_chat_completion("How to optimize building energy efficiency?")
```

## 5. 优化建议
### 5.1 性能提升
1. 使用`uvloop`替代默认事件循环：
```bash
pip install uvloop
```

2. 启用PyPy解释器（需替换venv环境）

### 5.2 安全加固
1. 使用`.env`文件存储敏感信息（已配置）
2. 添加速率限制中间件：
```python
# utils.py
import time
from functools import lru_cache

class RateLimiter:
    def __init__(self, max_calls=10, period=60):
        self.max_calls = max_calls
        self.period = period
        self.calls = {}

    def __call__(self, func):
        def wrapper(*args, **kwargs):
            now = time.time()
            key = (func.__name__, args, frozenset(kwargs.items()))
            
            if key not in self.calls:
                self.calls[key] = []
                
            # 清理过期调用
            self.calls[key] = [t for t in self.calls[key] if now - t < self.period]
            
            if len(self.calls[key]) >= self.max_calls:
                raise Exception("达到API调用频率限制")
                
            self.calls[key].append(now)
            return func(*args, **kwargs)
        return wrapper
```

## 6. 错误处理指南
| 错误类型 | 解决方案 |
|---------|----------|
| 429 Too Many Requests | 启用rate_limiter或增加重试间隔 |
| SSL证书错误 | 添加verify=False参数（不推荐生产环境） |
| 流解析失败 | 检查网络连接和API响应格式 |
| API密钥无效 | 在`.env`中重新配置正确密钥 |

## 7. 扩展方向
1. **多模型支持**：
```python
# config.py
SUPPORTED_MODELS = {
    "gpt-4o": "openai/gpt-4o",
    "claude": "anthropic/claude-3-opus",
    "qwen": "qwen/qwen-3-32b"
}
```

2. **历史记录持久化**：
```python
# 添加SQLite存储
import sqlite3
conn = sqlite3.connect('chat_history.db')
cursor = conn.cursor()
cursor.execute('''
    CREATE TABLE IF NOT EXISTS history
    (id INTEGER PRIMARY KEY, 
     question TEXT,
     response TEXT,
     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP)
''')
```

## 8. 验收测试
```python
# test_main.py
import unittest
from main import stream_chat_completion

class TestChatCompletion(unittest.TestCase):
    def test_stream_response(self):
        result = stream_chat_completion("Test query")
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)

if __name__ == '__main__':
    unittest.main()
```

> ⚠️ 安全提示：请将`.env`文件加入`.gitignore`，禁止提交到代码仓库
```gitignore
# .gitignore
.env
venv/
__pycache__
*.pyc
```
```

该文档在保持原代码功能的基础上，提供了完整的VSCode开发工作流支持，包含：
1. 标准化项目结构配置
2. 调试/测试工具集成
3. 安全加固方案
4. 性能优化路径
5. 可扩展性设计
6. 错误处理指南

通过模块化设计和详细的配置说明，实现了"简单、快捷、有效"的核心诉求，同时预留了多模型支持、历史记录存储等扩展能力，符合奥卡姆剃刀原理的"必要性原则"。