﻿{
  "nodes": [
    {
      "nodeId": "pluginInput",
      "name": "插件开始",
      "intro": "自定义配置外部输入，使用插件时，仅暴露自定义配置的输入",
      "avatar": "core/workflow/template/workflowStart",
      "flowNodeType": "pluginInput",
      "showStatus": false,
      "position": {
        "x": 535.7465806305546,
        "y": -201.26482361861054
      },
      "version": "481",
      "inputs": [
        {
          "inputType": "input",
          "valueType": "string",
          "key": "飞书机器人地址",
          "label": "飞书机器人地址",
          "description": "",
          "isToolInput": false,
          "defaultValue": "",
          "editField": {
            "key": true
          },
          "dynamicParamDefaultValue": {
            "inputType": "reference",
            "valueType": "string",
            "required": true
          },
          "renderTypeList": [
            "input"
          ],
          "required": true,
          "canEdit": true,
          "value": ""
        },
        {
          "key": "发送的消息",
          "valueType": "string",
          "label": "发送的消息",
          "renderTypeList": [
            "reference"
          ],
          "required": true,
          "description": "",
          "canEdit": true,
          "value": "",
          "editField": {
            "key": true
          },
          "dynamicParamDefaultValue": {
            "inputType": "reference",
            "valueType": "string",
            "required": true
          }
        }
      ],
      "outputs": [
        {
          "id": "mv52BrPVE6bm",
          "key": "飞书机器人地址",
          "valueType": "string",
          "label": "飞书机器人地址",
          "type": "static"
        },
        {
          "id": "p0m68Dv5KaIp",
          "key": "发送的消息",
          "valueType": "string",
          "label": "发送的消息",
          "type": "static"
        }
      ]
    },
    {
      "nodeId": "pluginOutput",
      "name": "插件输出",
      "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出",
      "avatar": "core/workflow/template/pluginOutput",
      "flowNodeType": "pluginOutput",
      "showStatus": false,
      "position": {
        "x": 1776.027569211593,
        "y": -58.264823618610535
      },
      "version": "481",
      "inputs": [],
      "outputs": []
    },
    {
      "nodeId": "rKBYGQuYefae",
      "name": "HTTP 请求",
      "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）",
      "avatar": "core/workflow/template/httpRequest",
      "flowNodeType": "httpRequest468",
      "showStatus": true,
      "position": {
        "x": 1069.7228495148624,
        "y": -392.26482361861054
      },
      "version": "481",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "common:core.module.input.description.HTTP Dynamic Input",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "arrayAny",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectDataset",
              "selectApp"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpMethod",
          "renderTypeList": [
            "custom"
          ],
          "valueType": "string",
          "label": "",
          "value": "POST",
          "required": true,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpTimeout",
          "renderTypeList": [
            "custom"
          ],
          "valueType": "number",
          "label": "",
          "value": 30,
          "min": 5,
          "max": 600,
          "required": true,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpReqUrl",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "string",
          "label": "",
          "description": "common:core.module.input.description.Http Request Url",
          "placeholder": "https://api.ai.com/getInventory",
          "required": false,
          "value": "{{url}}",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_header_secret",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "object",
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpHeader",
          "renderTypeList": [
            "custom"
          ],
          "valueType": "any",
          "value": [],
          "label": "",
          "description": "common:core.module.input.description.Http Request Header",
          "placeholder": "common:core.module.input.description.Http Request Header",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpParams",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "value": [],
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpJsonBody",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "value": "{\r\n    \"msg_type\": \"text\",\r\n    \"content\": {\r\n        \"text\": \"{{text}}\"\r\n    }\r\n}",
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpFormBody",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "value": [],
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpContentType",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "string",
          "value": "json",
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "text",
          "valueType": "string",
          "label": "text",
          "renderTypeList": [
            "reference"
          ],
          "description": "",
          "canEdit": true,
          "editField": {
            "key": true,
            "valueType": true
          },
          "value": [
            "pluginInput",
            "p0m68Dv5KaIp"
          ],
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "arrayAny",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectDataset",
              "selectApp"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        },
        {
          "key": "url",
          "valueType": "string",
          "label": "url",
          "renderTypeList": [
            "reference"
          ],
          "description": "",
          "canEdit": true,
          "editField": {
            "key": true,
            "valueType": true
          },
          "value": [
            "pluginInput",
            "mv52BrPVE6bm"
          ],
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "arrayAny",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectDataset",
              "selectApp"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        }
      ],
      "outputs": [
        {
          "id": "error",
          "key": "error",
          "label": "workflow:request_error",
          "description": "HTTP请求错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "httpRawResponse",
          "key": "httpRawResponse",
          "required": true,
          "label": "workflow:raw_response",
          "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。",
          "valueType": "any",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "editField": {
            "key": true,
            "valueType": true
          }
        }
      ]
    },
    {
      "nodeId": "q3ccNXiZIHoS",
      "name": "系统配置",
      "intro": "",
      "avatar": "core/workflow/template/systemConfig",
      "flowNodeType": "pluginConfig",
      "position": {
        "x": 99.73879703925843,
        "y": -201.26482361861054
      },
      "version": "4811",
      "inputs": [],
      "outputs": []
    }
  ],
  "edges": [
    {
      "source": "pluginInput",
      "target": "rKBYGQuYefae",
      "sourceHandle": "pluginInput-source-right",
      "targetHandle": "rKBYGQuYefae-target-left"
    },
    {
      "source": "rKBYGQuYefae",
      "target": "pluginOutput",
      "sourceHandle": "rKBYGQuYefae-source-right",
      "targetHandle": "pluginOutput-target-left"
    }
  ],
  "chatConfig": {
    "questionGuide": {
      "open": false,
      "model": "gpt-4o-mini",
      "customPrompt": ""
    },
    "ttsConfig": {
      "type": "web"
    },
    "whisperConfig": {
      "open": false,
      "autoSend": false,
      "autoTTSResponse": false
    },
    "chatInputGuide": {
      "open": false,
      "textList": [],
      "customUrl": ""
    },
    "instruction": "",
    "autoExecute": {
      "open": false,
      "defaultPrompt": ""
    },
    "variables": [],
    "welcomeText": ""
  }
}