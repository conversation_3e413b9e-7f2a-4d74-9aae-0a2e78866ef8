{"name": "chatbot-ui", "version": "2.0.0", "private": true, "scripts": {"chat": "supabase start && npm run db-types && npm run dev", "restart": "supabase stop && npm run chat", "update": "git pull origin main && npm run db-migrate && npm run db-types", "prepare": "husky install", "clean": "npm run lint:fix && npm run format:write", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "analyze": "ANALYZE=true npm run build", "preview": "next build && next start", "type-check": "tsc --noEmit", "format:write": "prettier --write \"{app,lib,db,components,context,types}/**/*.{ts,tsx}\" --cache", "format:check": "prettier --check \"{app,lib,db,components,context,types}**/*.{ts,tsx}\" --cache", "db-reset": "supabase db reset && npm run db-types", "db-migrate": "supabase migration up && npm run db-types", "db-types": "supabase gen types typescript --local > supabase/types.ts", "db-pull": "supabase db remote commit", "db-push": "supabase db push", "test": "jest"}, "dependencies": {"@anthropic-ai/sdk": "^0.18.0", "@apidevtools/json-schema-ref-parser": "^11.1.0", "@azure/openai": "^1.0.0-beta.8", "@google/generative-ai": "^0.11.4", "@hookform/resolvers": "^3.3.2", "@mistralai/mistralai": "^0.0.8", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/ssr": "^0.0.10", "@supabase/supabase-js": "^2.38.4", "@tabler/icons-react": "^2.40.0", "@vercel/analytics": "^1.1.1", "@vercel/edge-config": "^0.4.1", "@xenova/transformers": "^2.13.4", "ai": "^2.2.31", "class-variance-authority": "^0.7.0", "cmdk": "^0.2.0", "d3-dsv": "^2.0.0", "date-fns": "^2.30.0", "endent": "^2.1.0", "gpt-tokenizer": "^2.1.2", "i18next": "^23.7.16", "i18next-resources-to-backend": "^1.2.0", "langchain": "^0.0.213", "lucide-react": "^0.292.0", "mammoth": "^1.6.0", "next": "^14.1.0", "next-i18n-router": "^5.2.0", "next-pwa": "5.6.0", "next-themes": "^0.2.1", "openai": "^4.23.0", "pdf-parse": "^1.1.1", "react": "^18", "react-day-picker": "^8.9.1", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-i18next": "^14.0.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.5.3", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sonner": "^1.3.1", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.2", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.11", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.11", "@types/uuid": "^9.0.7", "autoprefixer": "^10.4.16", "clsx": "^2.0.0", "eslint": "^8", "eslint-config-next": "^14.0.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.13.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "prettier": "^3.1.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5"}}