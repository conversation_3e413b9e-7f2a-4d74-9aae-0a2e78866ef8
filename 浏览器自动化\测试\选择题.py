import tkinter as tk
from tkinter import messagebox

class QuizApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("选择题测试")
        self.root.geometry("600x400")

        # 题目数据
        self.questions = [
            ("Python中哪个关键字用于定义函数？", ["def", "function", "func", "define"], "A"),
            ("以下哪个是Python的数据类型？", ["int", "string", "boolean", "以上都是"], "D"),
            ("Python中如何创建列表？", ["[]", "{}", "()", "以上都不对"], "A"),
            ("Python中哪个方法用于向列表添加元素？", ["add()", "append()", "insert()", "push()"], "B"),
            ("Python中如何获取字符串长度？", ["length()", "size()", "len()", "count()"], "C")
        ]

        # 初始化变量
        self.current_index = 0
        self.score = 0
        self.var = tk.StringVar()
        self.options = []

        self.create_gui()
        self.show_question()

    def create_gui(self):
        """创建图形界面"""
        # 题目显示
        self.question_label = tk.Label(self.root, text="", font=("Arial", 12), wraplength=500)
        self.question_label.pack(pady=20)

        # 选项按钮
        for i in range(4):
            rb = tk.Radiobutton(self.root, variable=self.var, value=chr(65 + i),
                               font=("Arial", 10), anchor="w")
            rb.pack(anchor="w", padx=50, pady=2)
            self.options.append(rb)

        # 提交按钮
        tk.Button(self.root, text="提交答案", command=self.check_answer,
                 font=("Arial", 10), bg="#4CAF50", fg="white").pack(pady=20)

        # 进度显示
        self.progress_label = tk.Label(self.root, text="", font=("Arial", 9), fg="gray")
        self.progress_label.pack()

    def show_question(self):
        """显示当前题目"""
        if self.current_index < len(self.questions):
            question, options, _ = self.questions[self.current_index]

            # 显示题目和选项
            self.question_label.config(text=f"题目 {self.current_index + 1}: {question}")
            for i, option in enumerate(options):
                self.options[i].config(text=f"{chr(65 + i)}. {option}")

            # 清除选择并更新进度
            self.var.set("")
            self.progress_label.config(text=f"进度: {self.current_index + 1}/{len(self.questions)} | 得分: {self.score}")
        else:
            self.show_result()

    def check_answer(self):
        """检查答案并进入下一题"""
        if not self.var.get():
            messagebox.showwarning("提示", "请选择一个答案！")
            return

        # 检查答案
        correct_answer = self.questions[self.current_index][2]
        if self.var.get() == correct_answer:
            self.score += 1
            messagebox.showinfo("正确", "回答正确！")
        else:
            messagebox.showinfo("错误", f"回答错误！正确答案是: {correct_answer}")

        # 下一题
        self.current_index += 1
        self.show_question()

    def show_result(self):
        """显示最终结果"""
        total = len(self.questions)
        percentage = (self.score / total) * 100

        result = f"测试完成！\n总题数: {total}\n正确: {self.score}\n正确率: {percentage:.1f}%"
        messagebox.showinfo("测试结果", result)
        self.root.quit()

if __name__ == "__main__":
    app = QuizApp()
    app.root.mainloop()