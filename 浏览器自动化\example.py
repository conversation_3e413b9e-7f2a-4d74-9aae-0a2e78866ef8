from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

chrome_options = Options()
# 设置正确的驱动路径
service = Service(executable_path="./chromedriver-mac-arm64/chromedriver")
driver = webdriver.Chrome(service=service, options=chrome_options)

# 打开登录页面
driver.get("https://example.com/login")

# 定位用户名输入框并输入用户名
username_input = driver.find_element(By.ID, "username")
username_input.send_keys("testuser")

# 定位密码输入框并输入密码
password_input = driver.find_element(By.ID, "password")
password_input.send_keys("password123")

# 定位登录按钮并点击
login_button = driver.find_element(By.ID, "login-button")
login_button.click()

# 验证登录是否成功
assert "Welcome" in driver.page_source

# 关闭浏览器
driver.quit()