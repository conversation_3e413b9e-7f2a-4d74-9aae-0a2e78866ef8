﻿{
  "nodes": [
    {
      "nodeId": "userGuide",
      "name": "系统配置",
      "intro": "可以配置应用的系统参数",
      "avatar": "core/workflow/template/systemConfig",
      "flowNodeType": "userGuide",
      "position": {
        "x": 262.2732338817093,
        "y": -476.00241136598146
      },
      "version": "481",
      "inputs": [
        {
          "key": "welcomeText",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "string",
          "label": "core.app.Welcome Text",
          "value": ""
        },
        {
          "key": "variables",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "core.app.Chat Variable",
          "value": []
        },
        {
          "key": "questionGuide",
          "valueType": "any",
          "renderTypeList": [
            "hidden"
          ],
          "label": "core.app.Question Guide",
          "value": {
            "open": false
          }
        },
        {
          "key": "tts",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": {
            "type": "web"
          }
        },
        {
          "key": "whisper",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": {
            "open": false,
            "autoSend": false,
            "autoTTSResponse": false
          }
        },
        {
          "key": "scheduleTrigger",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": null
        }
      ],
      "outputs": []
    },
    {
      "nodeId": "448745",
      "name": "流程开始",
      "intro": "",
      "avatar": "core/workflow/template/workflowStart",
      "flowNodeType": "workflowStart",
      "position": {
        "x": 295.8944548701009,
        "y": 110.81336038514848
      },
      "version": "481",
      "inputs": [
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "toolDescription": "用户问题",
          "debugLabel": ""
        }
      ],
      "outputs": [
        {
          "id": "userChatInput",
          "key": "userChatInput",
          "label": "core.module.input.label.user question",
          "valueType": "string",
          "type": "static",
          "description": ""
        }
      ]
    },
    {
      "nodeId": "NOgbnBzUwDgT",
      "name": "工具调用",
      "intro": "通过AI模型自动选择一个或多个功能块进行调用，也可以对插件进行调用。",
      "avatar": "core/workflow/template/toolCall",
      "flowNodeType": "tools",
      "showStatus": true,
      "position": {
        "x": 1028.8358722416106,
        "y": -500.8755882990822
      },
      "version": "4.9.2",
      "inputs": [
        {
          "key": "model",
          "renderTypeList": [
            "settingLLMModel",
            "reference"
          ],
          "label": "core.module.input.label.aiModel",
          "valueType": "string",
          "llmModelType": "all",
          "value": "qwen-max",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "temperature",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": 0,
          "valueType": "number",
          "min": 0,
          "max": 10,
          "step": 1,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "maxToken",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": 2000,
          "valueType": "number",
          "min": 100,
          "max": 4000,
          "step": 50,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "aiChatVision",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "boolean",
          "value": true,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "aiChatReasoning",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "boolean",
          "value": true,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "aiChatTopP",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "number",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "aiChatStopSign",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "string",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "aiChatResponseFormat",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "string",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "aiChatJsonSchema",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "string",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "systemPrompt",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "max": 3000,
          "valueType": "string",
          "label": "core.ai.Prompt",
          "description": "core.app.tip.systemPromptTip",
          "placeholder": "core.app.tip.chatNodeSystemPromptTip",
          "value": "",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "history",
          "renderTypeList": [
            "numberInput",
            "reference"
          ],
          "valueType": "chatHistory",
          "label": "core.module.input.label.chat history",
          "description": "workflow:max_dialog_rounds",
          "required": true,
          "min": 0,
          "max": 50,
          "value": 6,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "fileUrlList",
          "renderTypeList": [
            "reference",
            "input"
          ],
          "label": "app:workflow.user_file_input",
          "debugLabel": "文件链接",
          "description": "app:workflow.user_file_input_desc",
          "valueType": "arrayString",
          "toolDescription": ""
        },
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "value": [
            "448745",
            "userChatInput"
          ],
          "toolDescription": "用户输入的问题（问题需要完善）",
          "debugLabel": ""
        }
      ],
      "outputs": [
        {
          "id": "answerText",
          "key": "answerText",
          "label": "common:core.module.output.label.Ai response content",
          "description": "将在 stream 回复完毕后触发",
          "valueType": "string",
          "type": "static"
        },
        {
          "id": "NodeOutputKeyEnum.answerText",
          "key": "NodeOutputKeyEnum.answerText",
          "label": "core.module.output.label.Ai response content",
          "description": "core.module.output.description.Ai response content",
          "valueType": "string",
          "type": "FlowNodeOutputTypeEnum.static"
        }
      ]
    },
    {
      "nodeId": "GMELVPxHfpg5",
      "name": "HTTP 请求",
      "intro": "调用谷歌搜索，查询相关内容",
      "avatar": "core/workflow/template/httpRequest",
      "flowNodeType": "httpRequest468",
      "showStatus": true,
      "position": {
        "x": 1005.4777753640342,
        "y": 319.4905539380939
      },
      "version": "481",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "common:core.module.input.description.HTTP Dynamic Input",
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "arrayAny",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectDataset",
              "selectApp"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpMethod",
          "renderTypeList": [
            "custom"
          ],
          "valueType": "string",
          "label": "",
          "value": "GET",
          "required": true,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpTimeout",
          "renderTypeList": [
            "custom"
          ],
          "valueType": "number",
          "label": "",
          "value": 30,
          "min": 5,
          "max": 600,
          "required": true,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpReqUrl",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "string",
          "label": "",
          "description": "common:core.module.input.description.Http Request Url",
          "placeholder": "https://api.ai.com/getInventory",
          "required": false,
          "value": "https://www.googleapis.com/customsearch/v1",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_header_secret",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "object",
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpHeader",
          "renderTypeList": [
            "custom"
          ],
          "valueType": "any",
          "value": [],
          "label": "",
          "description": "common:core.module.input.description.Http Request Header",
          "placeholder": "common:core.module.input.description.Http Request Header",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpParams",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "value": [
            {
              "key": "q",
              "type": "string",
              "value": "{{query}}"
            },
            {
              "key": "cx",
              "type": "string",
              "value": "谷歌搜索cxID"
            },
            {
              "key": "key",
              "type": "string",
              "value": "谷歌搜索key"
            },
            {
              "key": "c2coff",
              "type": "string",
              "value": "1"
            },
            {
              "key": "start",
              "type": "string",
              "value": "1"
            },
            {
              "key": "end",
              "type": "string",
              "value": "20"
            },
            {
              "key": "dateRestrict",
              "type": "string",
              "value": "m[1]"
            }
          ],
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpJsonBody",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "value": "",
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpFormBody",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "value": [],
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "system_httpContentType",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "string",
          "value": "json",
          "label": "",
          "required": false,
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "valueType": "string",
          "renderTypeList": [
            "reference"
          ],
          "key": "query",
          "label": "query",
          "toolDescription": "谷歌搜索检索词",
          "required": true,
          "canEdit": true,
          "editField": {
            "key": true,
            "description": true
          },
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "arrayAny",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectDataset",
              "selectApp"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        }
      ],
      "outputs": [
        {
          "id": "error",
          "key": "error",
          "label": "workflow:request_error",
          "description": "HTTP请求错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "httpRawResponse",
          "key": "httpRawResponse",
          "label": "原始响应",
          "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。",
          "valueType": "any",
          "type": "static",
          "required": true
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "editField": {
            "key": true,
            "valueType": true
          }
        },
        {
          "id": "M5YmxaYe8em1",
          "type": "dynamic",
          "key": "prompt",
          "valueType": "string",
          "label": "prompt"
        }
      ]
    },
    {
      "nodeId": "poIbrrA8aiR0",
      "name": "代码运行",
      "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。",
      "avatar": "core/workflow/template/codeRun",
      "flowNodeType": "code",
      "showStatus": true,
      "position": {
        "x": 1711.805344753384,
        "y": 650.1023414708576
      },
      "version": "482",
      "inputs": [
        {
          "key": "system_addInputParam",
          "renderTypeList": [
            "addInputParam"
          ],
          "valueType": "dynamic",
          "label": "",
          "required": false,
          "description": "workflow:these_variables_will_be_input_parameters_for_code_execution",
          "editField": {
            "key": true,
            "valueType": true
          },
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "arrayAny",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectDataset",
              "selectApp"
            ],
            "showDescription": false,
            "showDefaultValue": true
          },
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "codeType",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": "js",
          "valueType": "string",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "code",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "value": "function main({data}){\n    const result = data.items.map((item) => ({\n      title: item.title,\n      link: item.link,\n      snippet: item.snippet\n    }))\n    return { prompt: JSON.stringify(result) }\n}",
          "valueType": "string",
          "debugLabel": "",
          "toolDescription": ""
        },
        {
          "key": "data",
          "valueType": "object",
          "label": "data",
          "renderTypeList": [
            "reference"
          ],
          "description": "",
          "canEdit": true,
          "editField": {
            "key": true,
            "valueType": true
          },
          "value": [
            "GMELVPxHfpg5",
            "httpRawResponse"
          ],
          "customInputConfig": {
            "selectValueTypeList": [
              "string",
              "number",
              "boolean",
              "object",
              "arrayString",
              "arrayNumber",
              "arrayBoolean",
              "arrayObject",
              "arrayAny",
              "any",
              "chatHistory",
              "datasetQuote",
              "dynamic",
              "selectDataset",
              "selectApp"
            ],
            "showDescription": false,
            "showDefaultValue": true
          }
        }
      ],
      "outputs": [
        {
          "id": "system_rawResponse",
          "key": "system_rawResponse",
          "label": "完整响应数据",
          "valueType": "object",
          "type": "static",
          "description": ""
        },
        {
          "id": "error",
          "key": "error",
          "label": "运行错误",
          "description": "代码运行错误信息，成功时返回空",
          "valueType": "object",
          "type": "static"
        },
        {
          "id": "system_addOutputParam",
          "key": "system_addOutputParam",
          "type": "dynamic",
          "valueType": "dynamic",
          "label": "",
          "editField": {
            "key": true,
            "valueType": true
          },
          "description": "将代码中 return 的对象作为输出，传递给后续的节点"
        },
        {
          "id": "qLUQfhG0ILRX",
          "type": "dynamic",
          "key": "prompt",
          "valueType": "string",
          "label": "prompt"
        }
      ]
    }
  ],
  "edges": [
    {
      "source": "448745",
      "target": "NOgbnBzUwDgT",
      "sourceHandle": "448745-source-right",
      "targetHandle": "NOgbnBzUwDgT-target-left"
    },
    {
      "source": "NOgbnBzUwDgT",
      "target": "GMELVPxHfpg5",
      "sourceHandle": "selectedTools",
      "targetHandle": "selectedTools"
    },
    {
      "source": "GMELVPxHfpg5",
      "target": "poIbrrA8aiR0",
      "sourceHandle": "GMELVPxHfpg5-source-right",
      "targetHandle": "poIbrrA8aiR0-target-left"
    }
  ],
  "chatConfig": {
    "scheduledTriggerConfig": {
      "cronString": "",
      "timezone": "Asia/Shanghai",
      "defaultPrompt": ""
    }
  }
}