# wxauto 微信自动化库产品说明文档

## 📋 产品概述

**wxauto** 是一个基于 UI 自动化技术的微信客户端控制库，版本 `3.9.8.15`。该库通过 Windows UI Automation API 实现对微信桌面客户端的自动化操作，支持消息收发、联系人管理、文件传输等核心功能。

### 核心特性
- 🤖 **全自动化操作**: 支持消息发送、接收、文件传输等完整微信功能
- 🌍 **多语言支持**: 支持简体中文、繁体中文、英文界面
- 👥 **联系人管理**: 好友列表获取、新好友申请处理、群成员管理
- 📁 **文件操作**: 支持各种格式文件的发送和接收
- 🔍 **消息监听**: 实时监听新消息，支持多会话同时监听
- 🎨 **彩色输出**: 提供丰富的终端彩色输出功能

## 🏗️ 技术架构

### 模块结构
```
wxauto/
├── __init__.py          # 包入口，导出主类
├── wxauto.py           # 主控制类 (610行)
├── elements.py         # UI元素封装 (628行)
├── utils.py            # 工具函数集 (269行)
├── languages.py        # 多语言配置 (91行)
├── color.py            # 彩色输出 (254行)
└── errors.py           # 自定义异常 (3行)
```

### 核心依赖
- **uiautomation**: UI自动化核心库
- **win32gui/win32api**: Windows API调用
- **PIL**: 图像处理和截图
- **psutil**: 进程管理
- **pyperclip**: 剪贴板操作

## 🚀 快速开始

### 基本使用
```python
from wxauto import WeChat

# 初始化微信实例
wx = WeChat()

# 发送消息
wx.SendMsg('Hello World!', '文件传输助手')

# 发送文件
wx.SendFiles(r'C:\path\to\file.txt', '文件传输助手')

# 获取所有消息
messages = wx.GetAllMessage()
```

### 消息监听
```python
# 添加监听对象
wx.AddListenChat('好友名称', savepic=True)

# 获取新消息
new_messages = wx.GetListenMessage()
```

## 📚 核心功能详解

### 1. WeChat 主类

#### 初始化参数
- `language`: 语言设置 ('cn'|'cn_t'|'en')，默认简体中文

#### 主要方法

**消息操作**
- `SendMsg(msg, who=None, clear=True)`: 发送文本消息
- `SendFiles(filepath, who=None)`: 发送文件
- `GetAllMessage(savepic=False)`: 获取所有消息
- `GetNextNewMessage(savepic=False)`: 获取下一条新消息

**会话管理**
- `ChatWith(who, notfound='ignore')`: 切换到指定聊天
- `GetSessionList(reset=False, newmessage=False)`: 获取会话列表
- `CurrentChat()`: 获取当前聊天对象名

**联系人功能**
- `GetAllFriends(keywords=None)`: 获取好友列表
- `GetNewFriends()`: 获取新好友申请
- `GetGroupMembers()`: 获取群成员列表

**监听功能**
- `AddListenChat(who, savepic=False)`: 添加监听对象
- `GetListenMessage()`: 获取监听消息

### 2. 消息类型系统

#### 消息类型
- `SysMessage`: 系统消息
- `TimeMessage`: 时间消息
- `RecallMessage`: 撤回消息
- `SelfMessage`: 自己发送的消息
- `FriendMessage`: 好友消息

#### 消息属性
- `sender`: 发送者
- `content`: 消息内容
- `id`: 消息ID
- `type`: 消息类型

### 3. UI元素类

#### ChatWnd (聊天窗口)
```python
chat = ChatWnd('好友名称')
chat.SendMsg('消息内容')
chat.SendFiles(['文件路径'])
messages = chat.GetAllMessage()
```

#### WeChatImage (图片处理)
```python
# 在图片预览窗口中
img = WeChatImage()
img.Save('保存路径')
img.OCR()  # 文字识别
img.Next()  # 下一张
img.Previous()  # 上一张
```

#### ContactWnd (联系人窗口)
```python
contact = ContactWnd()
friends = contact.GetAllFriends()
contact.Search('搜索关键词')
```

## 🛠️ 工具函数

### 剪贴板操作
- `SetClipboardText(text)`: 设置剪贴板文本
- `SetClipboardFiles(paths)`: 设置剪贴板文件

### 窗口操作
- `FindWindow(classname, name)`: 查找窗口
- `Click(rect)`: 点击指定区域
- `GetPathByHwnd(hwnd)`: 获取窗口程序路径

### 时间处理
- `ParseWeChatTime(time_str)`: 解析微信时间格式

## 🌍 多语言支持

### 支持的语言
- `cn`: 简体中文 (默认)
- `cn_t`: 繁体中文
- `en`: 英文

### 语言配置
```python
# 使用英文界面
wx = WeChat(language='en')

# 使用繁体中文
wx = WeChat(language='cn_t')
```

## 🎨 彩色输出功能

### Print 类 (彩色打印)
```python
from wxauto.color import Print

Print.red('错误信息')
Print.green('成功信息')
Print.blue('提示信息')
Print.random('随机颜色')
```

### Input 类 (彩色输入)
```python
from wxauto.color import Input

name = Input.cyan('请输入姓名: ')
password = Input.yellow('请输入密码: ')
```

### Warnings 类 (彩色警告)
```python
from wxauto.color import Warnings

Warnings.lightred('警告信息')
Warnings.lightyellow('注意事项')
```

## ⚠️ 注意事项

### 版本兼容性
- 库版本: 3.9.8.15
- 建议使用对应版本的微信客户端
- 版本不匹配时会显示警告信息

### 使用限制
- 仅支持 Windows 系统
- 需要微信桌面客户端已登录
- 部分功能依赖微信界面语言设置

### 性能考虑
- 好友列表获取速度约 6-8个/秒
- 大量操作时建议添加适当延时
- 监听功能会占用一定系统资源

## 🔧 故障排除

### 常见问题
1. **初始化失败**: 确保微信客户端已启动并登录
2. **找不到目标**: 检查好友名称是否正确，优先使用备注名
3. **发送失败**: 检查文件路径是否存在，网络连接是否正常
4. **版本警告**: 更新微信客户端或库版本

### 调试建议
- 使用 `CurrentChat()` 确认当前聊天对象
- 检查 `GetSessionList()` 获取可用会话
- 启用 `savepic=True` 自动保存聊天图片

## 📈 最佳实践

### 代码示例
```python
import time
from wxauto import WeChat

# 初始化
wx = WeChat()

# 批量发送消息
friends = ['好友1', '好友2', '好友3']
message = '群发消息内容'

for friend in friends:
    try:
        wx.SendMsg(message, friend)
        print(f'✅ 已发送给 {friend}')
        time.sleep(1)  # 避免发送过快
    except Exception as e:
        print(f'❌ 发送失败 {friend}: {e}')

# 监听新消息
wx.AddListenChat('重要群聊', savepic=True)
while True:
    new_msgs = wx.GetListenMessage()
    if new_msgs:
        for chat, messages in new_msgs.items():
            for msg in messages:
                print(f'📨 {msg.sender}: {msg.content}')
    time.sleep(2)
```

## 📄 更新日志

### v3.9.8.15
- 完善多语言支持
- 优化消息监听机制
- 增强文件传输功能
- 修复已知问题

---

**开发者**: Cluic
**更新时间**: 2024-03-21
**文档生成时间**: 2025-01-23